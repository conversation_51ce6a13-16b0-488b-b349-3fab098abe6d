package admin

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/auth/permissions"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/reservation"
	"errors"
	"gorm.io/gorm"
)

var (
	ErrUnsupportedTable = errors.New("the provided table doesn't support editing sessions")
)

func UpdateEditingSession(r *shared.AppContext, params Params) result.Result[reservation.ReservableEntity] {
	var res reservation.ReservableEntity
	switch params.Table {
	case "content":
		var c content.Content
		err := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
			if err := tx.Where("id = ? AND workspace = ?", params.ID, params.Workspace).First(&c).Error; err != nil {
				return err
			}
			if err := permissions.Evaluate(r.Account(), c, "update"); err != nil {
				return err
			}
			if err := c.StartOrExtendSession(params.ReservableParams, r.Account()); err != nil {
				return err
			}
			return tx.Save(&c).Error
		})
		if err != nil {
			return result.Error(err, res)
		}
		res.ID = c.ID
		res.Reservable = c.Reservable
		return result.Success(res)
	default:
		return result.Error(ErrUnsupportedTable, res)
	}
}

func EndEditingSession(r *shared.AppContext, params Params) result.EmptyResult {
	if params.EditingSession == nil {
		return result.ErrorEmpty(errors.New("invalid reservation key"))
	}
	switch params.Table {
	case "content":
		if err := r.TenantDatabase().
			Table("content").
			Where("id = ? AND workspace = ?", params.ID, params.Workspace).
			Where("editing_session = ?", params.EditingSession).
			Where("current_editor = ?", r.Account().ID).
			Update("editing_session", nil).
			Error; err != nil {
			return result.ErrorEmpty(err)
		}
		return result.SuccessEmpty()
	default:
		return result.ErrorEmpty(ErrUnsupportedTable)
	}
}

func EndExtendedLock(r *shared.AppContext, params Params) result.Result[reservation.ReservableEntity] {
	var res reservation.ReservableEntity

	switch params.Table {
	case "content":
		var c content.Content
		err := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
			if err := tx.Where("id = ? and workspace = ?", params.ID, params.Workspace).First(&c).Error; err != nil {
				return err
			}
			if !c.IsEditingSessionForUserOrAvailable(params.EditingSession, &r.Account().ID) {
				return reservation.ErrEditingSessionConflict
			}
			c.ExtendedLock = nil
			return tx.Save(&c).Error
		})
		if err != nil {
			return result.Error(err, res)
		}

		res.ID = c.ID
		res.Reservable = c.Reservable
		return result.Success(res)

	default:
		return result.Error(ErrUnsupportedTable, res)
	}
}

func OverrideExtendedLock(r *shared.AppContext, params BaseRequiredFields) result.Result[reservation.ReservableEntity] {
	var res reservation.ReservableEntity

	if !r.Account().IsAdmin {
		return result.Error(errors.New("must be an admin to override an extended lock"), res)
	}

	switch params.Table {
	case "content":
		var c content.Content
		if err := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
			if err := tx.Where("id = ? and workspace = ?", params.ID, params.Workspace).First(&c).Error; err != nil {
				return err
			}
			if c.IsSessionLocked(nil) {
				return reservation.ErrEditingSessionConflict
			}
			c.ExtendedLock = nil
			c.CurrentEditor = nil
			return tx.Save(&c).Error
		}); err != nil {
			return result.Error(err, res)
		}

		res.ID = c.ID
		res.Reservable = c.Reservable
		return result.Success(res)

	default:
		return result.Error(ErrUnsupportedTable, res)
	}
}
